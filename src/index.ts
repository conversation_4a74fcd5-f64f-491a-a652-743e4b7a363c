interface Env {
  BUCKET: R2Bucket;
}

abstract class BaseRequest {

  protected constructor(protected readonly key: string) {
  }

  abstract execute(): Promise<Response>;
}

class CreateRequest extends BaseRequest {

  constructor(private readonly bucket: R2Bucket, key: string) {
    super(key);
  }

  async execute(): Promise<Response> {
    const { key, uploadId } = await this.bucket.createMultipartUpload(this.key);
    return new Response(JSON.stringify({ key, uploadId }));
  }
}

interface CompleteBody {
  parts: R2UploadedPart[];
}

abstract class UploadRequest extends BaseRequest {
  protected readonly upload: R2MultipartUpload;

  constructor(bucket: R2Bucket, key: string, uploadId: string) {
    super(key);
    this.upload = bucket.resumeMultipartUpload(key, uploadId);
  }
}

class CompleteRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string, private readonly body: Promise<CompleteBody>) {
    super(bucket, key, uploadId);
  }

  async execute(): Promise<Response> {
    const completeBody = await this.body;
    if (completeBody === null) {
      return new Response("Missing or incomplete body", {
        status: 400
      });
    }

    // Error handling in case the multipart upload does not exist anymore
    try {
      const object = await this.upload.complete(completeBody.parts);
      return new Response(null, {
        headers: {
          etag: object.httpEtag,
        }
      });
    } catch (error: any) {
      return new Response(error.message, { status: 400 });
    }
  }
}

class UploadPartRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string, private readonly partNumber: string, private readonly body: Request['body']) {
    super(bucket, key, uploadId);
  }

  async execute(): Promise<Response> {
    if (this.body === null) {
      return new Response("Missing request body", { status: 400 });
    }
    const partNumber = parseInt(this.partNumber);
    if (isNaN(partNumber)) {
      return new Response("Invalid part number", { status: 400 });
    }
    try {
      const uploadedPart = await this.upload.uploadPart(partNumber, this.body);
      return new Response(JSON.stringify(uploadedPart));
    } catch (error: any) {
      return new Response(error.message, { status: 400 });
    }
  }
}

class AbortRequest extends UploadRequest {

  constructor(bucket: R2Bucket, key: string, uploadId: string) {
    super(bucket, key, uploadId);
  }

  async execute(): Promise<Response> {
    try {
      await this.upload.abort();
    } catch (error: any) {
      return new Response(error.message, { status: 400 });
    }
    return new Response(null, { status: 204 });
  }
}

export default {
  async fetch(request, env): Promise<Response> {
    const pathParts = new URL(request.url).pathname.slice(1).split('/');
    if (request.method === "POST") {
      if (pathParts.length === 1) {
        // POST /{key} - Create multipart upload
        return await new CreateRequest(env.BUCKET, pathParts[0]).execute();
      }
      if (pathParts.length == 2) {
        // POST /{key}/{uploadId} - Complete multipart upload
        return await new CompleteRequest(env.BUCKET, pathParts[0], pathParts[1], request.json()).execute();
      }
    }
    if (request.method === "PUT") {
      if (pathParts.length == 3) {
        // PUT /{key}/{uploadId}/{partNumber} - Upload a part
        return await new UploadPartRequest(env.BUCKET, pathParts[0], pathParts[1], pathParts[2], request.body).execute();
      }
    }
    if (request.method === "DELETE" && pathParts.length == 2) {
      // DELETE /{key}/{uploadId} - Abort multipart upload
      return await new AbortRequest(env.BUCKET, pathParts[0], pathParts[1]).execute();
    }
    return new Response("Not Found", { status: 404 });
  },
} satisfies ExportedHandler<Env>;