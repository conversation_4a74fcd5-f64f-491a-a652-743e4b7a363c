{"name": "api-upload", "version": "1.0.0", "private": true, "type": "module", "main": "src/index.ts", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "test": "vitest", "test:watch": "vitest --watch", "type-check": "tsc --noEmit"}, "devDependencies": {"@cloudflare/workers-types": "^4.20250803.0", "@types/node": "^20.10.0", "typescript": "^5.9.2", "vitest": "^3.2.4", "vitest-environment-miniflare": "^2.14.4", "wrangler": "^4.27.0"}, "dependencies": {"hono": "^4.8.12"}}