/**
 * Basic tests for the multipart upload Worker using Hono
 */

import { describe, it, expect, beforeEach } from 'vitest';
import app from '../src/index';

// Mock environment for testing
const mockEnv = {
  BUCKET: {
    createMultipartUpload: async (key: string) => ({
      key,
      uploadId: 'test-upload-id-123'
    }),
    resumeMultipartUpload: (key: string, uploadId: string) => ({
      uploadPart: async (partNumber: number, body: any) => ({
        partNumber,
        etag: `"etag-${partNumber}"`
      }),
      complete: async (parts: any[]) => ({
        key,
        httpEtag: '"completed-etag"'
      }),
      abort: async () => {}
    }),
    get: async (key: string) => {
      if (key === 'existing-file.txt') {
        return {
          body: new ReadableStream(),
          httpEtag: '"file-etag"',
          writeHttpMetadata: (headers: Headers) => {
            headers.set('content-type', 'text/plain');
          }
        };
      }
      return null;
    },
    delete: async (key: string) => {}
  },
  ENVIRONMENT: 'test'
};

describe('Multipart Upload Worker', () => {
  it('should return health check', async () => {
    const req = new Request('http://localhost/health');
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.status).toBe('healthy');
  });

  it('should create multipart upload', async () => {
    const req = new Request('http://localhost/test-file.txt?action=mpu-create', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ metadata: { test: 'value' } })
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(201);
    const data = await res.json();
    expect(data.key).toBe('test-file.txt');
    expect(data.uploadId).toBe('test-upload-id-123');
  });

  it('should upload a part', async () => {
    const req = new Request('http://localhost/test-file.txt?action=mpu-uploadpart&uploadId=test-upload-id&partNumber=1', {
      method: 'PUT',
      body: 'test data'
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.partNumber).toBe(1);
    expect(data.etag).toBe('"etag-1"');
  });

  it('should complete multipart upload', async () => {
    const parts = [
      { partNumber: 1, etag: '"etag-1"' },
      { partNumber: 2, etag: '"etag-2"' }
    ];
    
    const req = new Request('http://localhost/test-file.txt?action=mpu-complete&uploadId=test-upload-id', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ parts })
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(200);
    const data = await res.json();
    expect(data.key).toBe('test-file.txt');
    expect(data.etag).toBe('"completed-etag"');
  });

  it('should abort multipart upload', async () => {
    const req = new Request('http://localhost/test-file.txt?action=mpu-abort&uploadId=test-upload-id', {
      method: 'DELETE'
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(204);
  });

  it('should get existing object', async () => {
    const req = new Request('http://localhost/existing-file.txt?action=get', {
      method: 'GET'
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(200);
    expect(res.headers.get('content-type')).toBe('text/plain');
  });

  it('should return 404 for non-existing object', async () => {
    const req = new Request('http://localhost/non-existing.txt?action=get', {
      method: 'GET'
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.error).toBe('ObjectNotFound');
  });

  it('should delete object', async () => {
    const req = new Request('http://localhost/test-file.txt?action=delete', {
      method: 'DELETE'
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(204);
  });

  it('should return 400 for invalid part number', async () => {
    const req = new Request('http://localhost/test-file.txt?action=mpu-uploadpart&uploadId=test-upload-id&partNumber=0', {
      method: 'PUT',
      body: 'test data'
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.error).toBe('BadRequest');
  });

  it('should return 400 for missing uploadId', async () => {
    const req = new Request('http://localhost/test-file.txt?action=mpu-uploadpart&partNumber=1', {
      method: 'PUT',
      body: 'test data'
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(400);
    const data = await res.json();
    expect(data.message).toContain('uploadId');
  });

  it('should return 404 for unknown endpoint', async () => {
    const req = new Request('http://localhost/unknown-endpoint', {
      method: 'GET'
    });
    
    const res = await app.fetch(req, mockEnv);
    
    expect(res.status).toBe(404);
    const data = await res.json();
    expect(data.error).toBe('NotFound');
  });
});
